import React from 'react';
import { render, screen } from '@testing-library/react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {
  ReactDndAdapter,
  ReactDndProviderFactory,
  useReactDndDrag,
  useReactDndDrop,
  wrapReactDndComponent,
} from './ReactDndAdapterV2';

// 抑制 React DnD ref 警告
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Function components cannot be given refs')
    ) {
      return;
    }
    originalConsoleError(...args);
  };
});

afterAll(() => {
  console.error = originalConsoleError;
});

describe('ReactDndAdapter', () => {
  describe('useDrag', () => {
    it('should return drag ref and isDragging state', () => {
      const TestComponent = () => {
        const { dragRef, isDragging } = useReactDndDrag({
          type: 'test-type',
          id: 'test-id',
        });

        return (
          <div ref={dragRef} data-testid='drag-target'>
            {isDragging ? 'Dragging' : 'Not Dragging'}
          </div>
        );
      };

      render(
        <DndProvider backend={HTML5Backend}>
          <TestComponent />
        </DndProvider>
      );

      expect(screen.getByTestId('drag-target').textContent).toBe('Not Dragging');
    });

    it('should respect disabled flag', () => {
      const TestComponent = () => {
        const { dragRef, isDragging } = useReactDndDrag({
          type: 'test-type',
          id: 'test-id',
          disabled: true,
        });

        return (
          <div ref={dragRef} data-testid='drag-target'>
            {isDragging ? 'Dragging' : 'Not Dragging'}
          </div>
        );
      };

      render(
        <DndProvider backend={HTML5Backend}>
          <TestComponent />
        </DndProvider>
      );

      expect(screen.getByTestId('drag-target').textContent).toBe('Not Dragging');
    });
  });

  describe('useDrop', () => {
    it('should return drop ref and drop states', () => {
      const TestComponent = () => {
        const { dropRef, isOver, canDrop } = useReactDndDrop({
          accept: 'test-type',
        });

        return (
          <div ref={dropRef} data-testid='drop-target'>
            {isOver ? 'Over' : 'Not Over'} - {canDrop ? 'Can Drop' : 'Cannot Drop'}
          </div>
        );
      };

      render(
        <DndProvider backend={HTML5Backend}>
          <TestComponent />
        </DndProvider>
      );

      expect(screen.getByTestId('drop-target').textContent).toBe('Not Over - Cannot Drop');
    });

    it('should call onDrop when item is dropped', () => {
      const mockOnDrop = jest.fn();
      const TestComponent = () => {
        const { dropRef } = useReactDndDrop({
          accept: 'test-type',
          onDrop: mockOnDrop,
        });

        return <div ref={dropRef} data-testid='drop-target' />;
      };

      render(
        <DndProvider backend={HTML5Backend}>
          <TestComponent />
        </DndProvider>
      );

      // Simulate drop event (simplified for test)
      expect(mockOnDrop).not.toHaveBeenCalled();
    });
  });

  describe('convertItemType', () => {
    it('should convert known types', () => {
      const adapter = new ReactDndAdapter();
      expect(adapter.convertItemType('vehicle-card')).toBe('vehicleCardDispatch');
      expect(adapter.convertItemType('task-card')).toBe('taskCard');
    });

    it('should return original type for unknown types', () => {
      const adapter = new ReactDndAdapter();
      expect(adapter.convertItemType('unknown-type')).toBe('unknown-type');
    });
  });

  describe('ReactDndProviderFactory', () => {
    it('should create a provider with HTML5 backend', () => {
      const factory = new ReactDndProviderFactory();
      const Provider = factory.createProvider();
      const { container } = render(
        <Provider>
          <div>Test</div>
        </Provider>
      );
      expect(container).toBeInTheDocument();
    });
  });

  describe('wrapReactDndComponent', () => {
    it('should wrap a component with drag and drop capabilities', () => {
      const TestComponent = ({ isDragging }: { isDragging?: boolean }) => (
        <div data-testid='wrapped-component'>{isDragging ? 'Dragging' : 'Not Dragging'}</div>
      );

      const WrappedComponent = wrapReactDndComponent(TestComponent, {
        type: 'test-type',
        id: 'test-id',
      });

      render(
        <DndProvider backend={HTML5Backend}>
          <WrappedComponent />
        </DndProvider>
      );

      expect(screen.getByTestId('wrapped-component').textContent).toBe('Not Dragging');
    });
  });
});
