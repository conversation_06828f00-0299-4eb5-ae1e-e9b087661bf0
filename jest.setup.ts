import '@testing-library/jest-dom';
import './src/__tests__/setup/global-mocks.setup';

// Mock Web API globals for Next.js API routes
global.Request =
  global.Request ||
  class Request {
    url: any;
    method: any;
    headers: Headers;
    body: any;
    constructor(input: any, init: any = {}) {
      this.url = typeof input === 'string' ? input : input.url;
      this.method = init.method || 'GET';
      this.headers = new Headers(init.headers);
      this.body = init.body || null;
    }
  };

global.Response =
  global.Response ||
  class Response {
    status: any;
    statusText: any;
    headers: Headers;
    body: any;
    constructor(body: any, init: any = {}) {
      this.status = init.status || 200;
      this.statusText = init.statusText || 'OK';
      this.headers = new Headers(init.headers);
      this.body = body;
    }
  };

global.Headers =
  global.Headers ||
  class Headers {
    private headers: Record<string, string> = {};
    
    constructor(init?: any) {
      if (init) {
        if (typeof init === 'object') {
          Object.entries(init).forEach(([key, value]) => {
            this.headers[key.toLowerCase()] = String(value);
          });
        }
      }
    }
    
    get(name: string): string | null {
      return this.headers[name.toLowerCase()] || null;
    }
    
    set(name: string, value: string): void {
      this.headers[name.toLowerCase()] = value;
    }
    
    append(name: string, value: string): void {
      const existing = this.get(name);
      this.set(name, existing ? `${existing}, ${value}` : value);
    }
    
    delete(name: string): void {
      delete this.headers[name.toLowerCase()];
    }
    
    has(name: string): boolean {
      return name.toLowerCase() in this.headers;
    }
    
    forEach(callback: (value: string, key: string) => void): void {
      Object.entries(this.headers).forEach(([key, value]) => {
        callback(value, key);
      });
    }
    
    entries(): IterableIterator<[string, string]> {
      return Object.entries(this.headers)[Symbol.iterator]();
    }
    
    keys(): IterableIterator<string> {
      return Object.keys(this.headers)[Symbol.iterator]();
    }
    
    values(): IterableIterator<string> {
      return Object.values(this.headers)[Symbol.iterator]();
    }
    
    [Symbol.iterator](): IterableIterator<[string, string]> {
      return this.entries();
    }
  };

// Mock FormData
global.FormData =
  global.FormData ||
  class FormData {
    private data: Map<string, any> = new Map();
    
    append(name: string, value: any): void {
      this.data.set(name, value);
    }
    
    get(name: string): any {
      return this.data.get(name);
    }
    
    getAll(name: string): any[] {
      const value = this.data.get(name);
      return value ? [value] : [];
    }
    
    has(name: string): boolean {
      return this.data.has(name);
    }
    
    set(name: string, value: any): void {
      this.data.set(name, value);
    }
    
    delete(name: string): void {
      this.data.delete(name);
    }
    
    entries(): IterableIterator<[string, any]> {
      return this.data.entries();
    }
    
    keys(): IterableIterator<string> {
      return this.data.keys();
    }
    
    values(): IterableIterator<any> {
      return this.data.values();
    }
    
    forEach(callback: (value: any, key: string) => void): void {
      this.data.forEach(callback);
    }
    
    [Symbol.iterator](): IterableIterator<[string, any]> {
      return this.data.entries();
    }
  };

// Mock fetch API for Jest environment
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve([]),
    text: () => Promise.resolve(''),
    headers: new Headers(),
    url: '',
    redirected: false,
    statusText: 'OK',
    type: 'basic',
    clone: jest.fn(),
    body: null,
    bodyUsed: false,
    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
    blob: () => Promise.resolve(new Blob()),
    formData: () => Promise.resolve(new FormData()),
  })
);

// Mock NextResponse
jest.mock('next/server', () => ({
  NextResponse: {
    json: (data: any, init: any = {}) => {
      return new Response(JSON.stringify(data), {
        status: init.status || 200,
        headers: {
          'Content-Type': 'application/json',
          ...init.headers,
        },
      });
    },
  },
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(),
};
Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', { value: sessionStorageMock });

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Suppress console warnings in tests
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// Mock PerformanceObserver for performance tests
global.PerformanceObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
  takeRecords: jest.fn(() => []),
}));

// Mock performance.memory
Object.defineProperty(performance, 'memory', {
  value: {
    usedJSHeapSize: 1000000,
    totalJSHeapSize: 2000000,
    jsHeapSizeLimit: 4000000,
  },
  writable: true,
});

// Mock URL and URLSearchParams
global.URL = global.URL || class URL {
  constructor(public href: string, base?: string) {
    // Simple URL implementation for tests
  }
};

global.URLSearchParams = global.URLSearchParams || class URLSearchParams {
  private params: Map<string, string> = new Map();
  
  constructor(init?: string | URLSearchParams | Record<string, string>) {
    if (typeof init === 'string') {
      // Parse query string
      init.split('&').forEach(pair => {
        const [key, value] = pair.split('=');
        if (key) this.params.set(decodeURIComponent(key), decodeURIComponent(value || ''));
      });
    } else if (init instanceof URLSearchParams) {
      init.forEach((value, key) => this.params.set(key, value));
    } else if (init && typeof init === 'object') {
      Object.entries(init).forEach(([key, value]) => this.params.set(key, value));
    }
  }
  
  get(name: string): string | null {
    return this.params.get(name);
  }
  
  set(name: string, value: string): void {
    this.params.set(name, value);
  }
  
  append(name: string, value: string): void {
    this.params.set(name, value);
  }
  
  delete(name: string): void {
    this.params.delete(name);
  }
  
  has(name: string): boolean {
    return this.params.has(name);
  }
  
  forEach(callback: (value: string, key: string) => void): void {
    this.params.forEach(callback);
  }
  
  toString(): string {
    const pairs: string[] = [];
    this.params.forEach((value, key) => {
      pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
    });
    return pairs.join('&');
  }
};
